{% extends "base.html" %}
{% load static %}

{% block title %}分红选股分析 - 股票分析系统{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          <i class="ti ti-gift me-2"></i>
          分红选股分析
        </h2>
        <div class="text-muted mt-1">基于分红数据的智能选股工具，发现高分红价值股</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button id="dividend-strategy" class="btn btn-primary">
            <i class="ti ti-target"></i>
            分红策略
          </button>
          <button id="yield-calculator" class="btn btn-outline-info">
            <i class="ti ti-calculator"></i>
            收益计算
          </button>
          <button id="export-dividend" class="btn btn-outline-secondary">
            <i class="ti ti-download"></i>
            导出数据
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 分红策略面板 -->
  <div id="strategy-panel" class="card mb-4" style="display: none;">
    <div class="card-header">
      <h3 class="card-title">
        <i class="ti ti-target me-2"></i>
        分红选股策略
      </h3>
      <div class="card-actions">
        <button id="close-strategy" class="btn btn-sm btn-outline-secondary">
          <i class="ti ti-x"></i>
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <div class="strategy-item" data-strategy="high-yield">
            <div class="alert alert-success">
              <h6><i class="ti ti-trending-up me-1"></i>高股息率</h6>
              <p class="mb-2 small">现金分红≥3元/10股</p>
              <button class="btn btn-sm btn-success">应用策略</button>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="strategy-item" data-strategy="stable-dividend">
            <div class="alert alert-info">
              <h6><i class="ti ti-shield me-1"></i>稳定分红</h6>
              <p class="mb-2 small">连续3年分红</p>
              <button class="btn btn-sm btn-info">应用策略</button>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="strategy-item" data-strategy="growth-dividend">
            <div class="alert alert-warning">
              <h6><i class="ti ti-chart-line me-1"></i>分红增长</h6>
              <p class="mb-2 small">分红逐年递增</p>
              <button class="btn btn-sm btn-warning">应用策略</button>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="strategy-item" data-strategy="bonus-shares">
            <div class="alert alert-primary">
              <h6><i class="ti ti-gift me-1"></i>高送转</h6>
              <p class="mb-2 small">送股+转增≥5股/10股</p>
              <button class="btn btn-sm btn-primary">应用策略</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分红统计卡片 -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="subheader">总分红记录</div>
            <div class="ms-auto lh-1">
              <div class="badge bg-blue">{{ total_dividends }}</div>
            </div>
          </div>
          <div class="h1 mb-3 mt-1 text-primary">{{ total_dividends }}</div>
          <div class="d-flex mb-2">
            <div>已实施</div>
            <div class="ms-auto">
              <span class="text-success">{{ implemented_count|default:0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="subheader">平均现金分红</div>
            <div class="ms-auto lh-1">
              <div class="badge bg-green">元/10股</div>
            </div>
          </div>
          <div class="h1 mb-3 mt-1 text-success">{{ avg_cash_dividend|floatformat:2|default:"0.00" }}</div>
          <div class="d-flex mb-2">
            <div>最高分红</div>
            <div class="ms-auto">
              <span class="text-success">{{ max_cash_dividend|floatformat:2|default:"0.00" }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="subheader">高分红股票</div>
            <div class="ms-auto lh-1">
              <div class="badge bg-orange">≥3元/10股</div>
            </div>
          </div>
          <div class="h1 mb-3 mt-1 text-warning">{{ high_dividend_count|default:0 }}</div>
          <div class="d-flex mb-2">
            <div>占比</div>
            <div class="ms-auto">
              <span class="text-warning">{{ high_dividend_ratio|floatformat:1|default:"0.0" }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="subheader">高送转股票</div>
            <div class="ms-auto lh-1">
              <div class="badge bg-red">≥5股/10股</div>
            </div>
          </div>
          <div class="h1 mb-3 mt-1 text-danger">{{ high_transfer_count|default:0 }}</div>
          <div class="d-flex mb-2">
            <div>占比</div>
            <div class="ms-auto">
              <span class="text-danger">{{ high_transfer_ratio|floatformat:1|default:"0.0" }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 筛选条件 -->
  <div class="card mb-3">
    <div class="card-header">
      <h3 class="card-title">筛选条件</h3>
      <div class="card-actions">
        <button id="toggle-advanced" class="btn btn-sm btn-outline-primary">
          <i class="ti ti-adjustments"></i>
          高级筛选
        </button>
      </div>
    </div>
    <div class="card-body">
      <form method="get" action="{% url 'financial_analysis:dividend_list' %}" id="filter-form">
        <!-- 基础筛选 -->
        <div class="row g-3 mb-3">
          <div class="col-md-2">
            <div class="form-label">年份筛选</div>
            <select name="year" class="form-select" id="year-select">
              <option value="">全部年份</option>
              {% for y in all_years %}
                <option value="{{ y }}" {% if y|stringformat:"i" == year %}selected{% endif %}>{{ y }}年</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-2">
            <div class="form-label">实施状态</div>
            <select name="implementation_status" class="form-select" id="status-select">
              <option value="">全部状态</option>
              {% for status in implementation_statuses %}
                <option value="{{ status }}" {% if status == implementation_status %}selected{% endif %}>{{ status }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-2">
            <div class="form-label">分红类型</div>
            <select name="dividend_type" class="form-select">
              <option value="">全部类型</option>
              <option value="cash_only" {% if request.GET.dividend_type == 'cash_only' %}selected{% endif %}>仅现金分红</option>
              <option value="with_bonus" {% if request.GET.dividend_type == 'with_bonus' %}selected{% endif %}>含送转股</option>
              <option value="high_transfer" {% if request.GET.dividend_type == 'high_transfer' %}selected{% endif %}>高送转</option>
            </select>
          </div>
          <div class="col-md-2">
            <div class="form-label">排序方式</div>
            <select name="sort" class="form-select">
              <option value="-registration_date" {% if sort == '-registration_date' %}selected{% endif %}>登记日期↓</option>
              <option value="-cash_dividend" {% if sort == '-cash_dividend' %}selected{% endif %}>现金分红↓</option>
              <option value="-share_dividend" {% if sort == '-share_dividend' %}selected{% endif %}>送股↓</option>
              <option value="-share_transfer" {% if sort == '-share_transfer' %}selected{% endif %}>转增↓</option>
              <option value="stock_code" {% if sort == 'stock_code' %}selected{% endif %}>股票代码↑</option>
            </select>
          </div>
          <div class="col-md-4">
            <div class="form-label">股票搜索</div>
            <div class="input-icon">
              <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="输入股票代码或名称..." id="search-input">
              <span class="input-icon-addon">
                <i class="ti ti-search"></i>
              </span>
            </div>
          </div>
        </div>

        <!-- 高级筛选条件 -->
        <div id="advanced-filters" class="border-top pt-3" style="display: none;">
          <div class="row g-3 mb-3">
            <div class="col-md-4">
              <div class="form-label">现金分红范围 (元/10股)</div>
              <div class="input-group">
                <input type="number" step="0.01" min="0" name="min_cash_dividend" value="{{ min_cash_dividend }}" class="form-control" placeholder="最小值">
                <span class="input-group-text">~</span>
                <input type="number" step="0.01" min="0" name="max_cash_dividend" value="{{ max_cash_dividend }}" class="form-control" placeholder="最大值">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-label">送股范围 (股/10股)</div>
              <div class="input-group">
                <input type="number" step="0.01" min="0" name="min_share_dividend" value="{{ min_share_dividend }}" class="form-control" placeholder="最小值">
                <span class="input-group-text">~</span>
                <input type="number" step="0.01" min="0" name="max_share_dividend" value="{{ max_share_dividend }}" class="form-control" placeholder="最大值">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-label">转增范围 (股/10股)</div>
              <div class="input-group">
                <input type="number" step="0.01" min="0" name="min_share_transfer" value="{{ min_share_transfer }}" class="form-control" placeholder="最小值">
                <span class="input-group-text">~</span>
                <input type="number" step="0.01" min="0" name="max_share_transfer" value="{{ max_share_transfer }}" class="form-control" placeholder="最大值">
              </div>
            </div>
          </div>

          <div class="row g-3 mb-3">
            <div class="col-md-3">
              <div class="form-label">分红频率</div>
              <select name="dividend_frequency" class="form-select">
                <option value="">不限</option>
                <option value="annual" {% if request.GET.dividend_frequency == 'annual' %}selected{% endif %}>年度分红</option>
                <option value="semi_annual" {% if request.GET.dividend_frequency == 'semi_annual' %}selected{% endif %}>半年度分红</option>
                <option value="quarterly" {% if request.GET.dividend_frequency == 'quarterly' %}selected{% endif %}>季度分红</option>
              </select>
            </div>
            <div class="col-md-3">
              <div class="form-label">登记日期范围</div>
              <div class="input-group">
                <input type="date" name="start_date" value="{{ request.GET.start_date }}" class="form-control">
                <span class="input-group-text">~</span>
                <input type="date" name="end_date" value="{{ request.GET.end_date }}" class="form-control">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-label">快捷筛选</div>
              <div class="d-flex gap-2 flex-wrap">
                <button type="button" class="btn btn-sm btn-outline-success quick-filter" data-filter="high-cash">高现金分红</button>
                <button type="button" class="btn btn-sm btn-outline-warning quick-filter" data-filter="high-bonus">高送转</button>
                <button type="button" class="btn btn-sm btn-outline-info quick-filter" data-filter="stable">稳定分红</button>
                <button type="button" class="btn btn-sm btn-outline-primary quick-filter" data-filter="recent">近期分红</button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetFilters()">重置</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 隐藏排序参数 -->
        <input type="hidden" name="sort" value="{{ sort }}" id="sort-field">
        <input type="hidden" name="secondary_sort" value="{{ secondary_sort }}" id="secondary-sort-field">
        <input type="hidden" name="rows_per_page" value="{{ rows_per_page }}" id="rows-per-page-field">

        <div class="d-flex justify-content-end">
          <button type="reset" class="btn btn-outline-secondary me-2">
            <i class="ti ti-refresh me-1"></i>
            重置
          </button>
          <button type="submit" class="btn btn-primary" id="search-btn">
            <i class="ti ti-search me-1"></i>
            查询
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- 分红数据表格 -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">分红记录 (共 {{ total_dividends }} 条)</h3>
      <div class="card-actions">
        <div class="d-flex gap-2">
          <button id="batch-collect" class="btn btn-sm btn-outline-primary">
            <i class="ti ti-heart"></i>
            批量收藏
          </button>
          <button id="yield-analysis" class="btn btn-sm btn-outline-info">
            <i class="ti ti-chart-pie"></i>
            收益分析
          </button>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>
                <input type="checkbox" id="select-all" class="form-check-input">
              </th>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>分红年度</th>
              <th>登记日期</th>
              <th class="sortable-header" data-sort="cash_dividend">
                现金分红
                {% if sort == 'cash_dividend' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-cash_dividend' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th class="sortable-header" data-sort="share_dividend">
                送股
                {% if sort == 'share_dividend' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-share_dividend' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th class="sortable-header" data-sort="share_transfer">
                转增
                {% if sort == 'share_transfer' %}<i class="ti ti-arrow-up"></i>
                {% elif sort == '-share_transfer' %}<i class="ti ti-arrow-down"></i>{% endif %}
              </th>
              <th>送转合计</th>
              <th>实施状态</th>
              <th>分红评级</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for dividend in page_obj %}
            <tr class="dividend-row" data-code="{{ dividend.stock_code }}" data-name="{{ dividend.stock_name }}">
              <td>
                <input type="checkbox" class="form-check-input dividend-checkbox" value="{{ dividend.stock_code }}">
              </td>
              <td>
                <a href="{% url 'market_data:stock_detail' dividend.stock_code %}" class="text-decoration-none fw-bold">
                  {{ dividend.stock_code }}
                </a>
              </td>
              <td>
                <span class="fw-bold">{{ dividend.stock_name }}</span>
                {% if dividend.cash_dividend and dividend.cash_dividend >= 3 %}
                <span class="badge bg-green ms-1">高分红</span>
                {% endif %}
                {% if dividend.share_dividend and dividend.share_transfer and dividend.share_dividend|add:dividend.share_transfer >= 5 %}
                <span class="badge bg-red ms-1">高送转</span>
                {% endif %}
              </td>
              <td>{{ dividend.registration_date|date:"Y" }}</td>
              <td>{{ dividend.registration_date|date:"Y-m-d" }}</td>
              <td>
                {% if dividend.cash_dividend %}
                  {% if dividend.cash_dividend >= 5 %}
                  <span class="text-success fw-bold">{{ dividend.cash_dividend }}元</span>
                  {% elif dividend.cash_dividend >= 3 %}
                  <span class="text-warning fw-bold">{{ dividend.cash_dividend }}元</span>
                  {% else %}
                  <span class="text-muted">{{ dividend.cash_dividend }}元</span>
                  {% endif %}
                {% else %}
                <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                {% if dividend.share_dividend %}
                <span class="text-info fw-bold">{{ dividend.share_dividend }}股</span>
                {% else %}
                <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                {% if dividend.share_transfer %}
                <span class="text-primary fw-bold">{{ dividend.share_transfer }}股</span>
                {% else %}
                <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                {% if dividend.share_dividend or dividend.share_transfer %}
                <span class="fw-bold">{{ dividend.share_dividend|default:0|add:dividend.share_transfer|default:0 }}股</span>
                {% else %}
                <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                {% if dividend.implementation_status == "已实施" %}
                <span class="badge bg-green">{{ dividend.implementation_status }}</span>
                {% elif dividend.implementation_status == "董事会预案" %}
                <span class="badge bg-blue">{{ dividend.implementation_status }}</span>
                {% elif dividend.implementation_status == "股东大会通过" %}
                <span class="badge bg-orange">{{ dividend.implementation_status }}</span>
                {% else %}
                <span class="badge bg-gray">{{ dividend.implementation_status }}</span>
                {% endif %}
              </td>
              <td>
                <div class="dividend-rating" data-rating="{{ dividend.dividend_rating|default:0 }}">
                  <div class="progress progress-sm">
                    <div class="progress-bar" style="width: {{ dividend.dividend_rating|default:0 }}%"></div>
                  </div>
                  <small class="text-muted">{{ dividend.dividend_rating|default:0 }}分</small>
                </div>
              </td>
              <td>
                <div class="btn-list">
                  <button class="btn btn-sm btn-outline-primary collect-btn"
                          data-code="{{ dividend.stock_code }}" data-name="{{ dividend.stock_name }}">
                    <i class="ti ti-heart"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-info analyze-btn"
                          data-code="{{ dividend.stock_code }}" data-name="{{ dividend.stock_name }}"
                          data-cash="{{ dividend.cash_dividend|default:0 }}"
                          data-share="{{ dividend.share_dividend|default:0 }}"
                          data-transfer="{{ dividend.share_transfer|default:0 }}">
                    <i class="ti ti-calculator"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="12" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <i class="ti ti-search" style="font-size: 48px; color: #ccc;"></i>
                  </div>
                  <p class="empty-title">暂无符合条件的分红记录</p>
                  <p class="empty-subtitle text-muted">
                    请尝试调整筛选条件或选择其他年份。
                  </p>
                  <div class="empty-action">
                    <button class="btn btn-primary" onclick="resetFilters()">
                      <i class="ti ti-refresh"></i>
                      重置筛选条件
                    </button>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
    </div>
  </div>
</div>
{% endblock content %}

{% block extra_css %}
<style>
  /* 分红策略面板样式 */
  .strategy-item {
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .strategy-item:hover {
    transform: translateY(-2px);
  }

  .strategy-item .alert {
    margin-bottom: 0;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  /* 分红评级样式 */
  .dividend-rating .progress {
    height: 8px;
    background-color: #e9ecef;
  }

  .dividend-rating .progress-bar {
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
  }

  /* 表格行悬停效果 */
  .dividend-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 快捷筛选按钮样式 */
  .quick-filter {
    transition: all 0.2s ease;
  }

  .quick-filter:hover {
    transform: scale(1.05);
  }

  .quick-filter.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
  }

  /* 收藏按钮样式 */
  .collect-btn.collected {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
  }

  /* 高级筛选面板样式 */
  #advanced-filters {
    background: rgba(0, 123, 255, 0.02);
    border-radius: 8px;
    padding: 20px;
  }

  /* 统计卡片美化 */
  .card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: none;
  }

  .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0;
  }

  /* 按钮美化 */
  .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  /* 标签美化 */
  .badge {
    font-size: 11px;
    padding: 4px 8px;
  }

  /* 动画效果 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .card {
    animation: fadeInUp 0.6s ease-out;
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  // 全局变量
  let selectedDividends = [];

  document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 计算分红评级
    calculateDividendRatings();
  });

  // 初始化事件监听器
  function initEventListeners() {
    // 分红策略按钮
    if (document.getElementById('dividend-strategy')) {
      document.getElementById('dividend-strategy').addEventListener('click', function() {
        const panel = document.getElementById('strategy-panel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
      });
    }

    // 关闭策略面板
    if (document.getElementById('close-strategy')) {
      document.getElementById('close-strategy').addEventListener('click', function() {
        document.getElementById('strategy-panel').style.display = 'none';
      });
    }

    // 高级筛选切换
    if (document.getElementById('toggle-advanced')) {
      document.getElementById('toggle-advanced').addEventListener('click', function() {
        const advancedFilters = document.getElementById('advanced-filters');
        advancedFilters.style.display = advancedFilters.style.display === 'none' ? 'block' : 'none';

        const icon = this.querySelector('i');
        if (advancedFilters.style.display === 'none') {
          icon.className = 'ti ti-adjustments';
          this.innerHTML = '<i class="ti ti-adjustments"></i> 高级筛选';
        } else {
          icon.className = 'ti ti-adjustments-horizontal';
          this.innerHTML = '<i class="ti ti-adjustments-horizontal"></i> 收起筛选';
        }
      });
    }

    // 全选/取消全选
    if (document.getElementById('select-all')) {
      document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.dividend-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
        updateSelectedDividends();
      });
    }

    // 分红复选框
    document.querySelectorAll('.dividend-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', updateSelectedDividends);
    });

    // 收藏按钮
    document.querySelectorAll('.collect-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const code = this.dataset.code;
        const name = this.dataset.name;
        toggleCollect(code, name, this);
      });
    });

    // 分析按钮
    document.querySelectorAll('.analyze-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const code = this.dataset.code;
        const name = this.dataset.name;
        const cash = parseFloat(this.dataset.cash) || 0;
        const share = parseFloat(this.dataset.share) || 0;
        const transfer = parseFloat(this.dataset.transfer) || 0;
        showDividendAnalysis(code, name, cash, share, transfer);
      });
    });

    // 快捷筛选按钮
    document.querySelectorAll('.quick-filter').forEach(btn => {
      btn.addEventListener('click', function() {
        const filter = this.dataset.filter;
        applyQuickFilter(filter, this);
      });
    });

    // 选股策略应用
    document.querySelectorAll('.strategy-item button').forEach(btn => {
      btn.addEventListener('click', function() {
        const strategy = this.closest('.strategy-item').dataset.strategy;
        applyStrategy(strategy);
      });
    });

    // 批量收藏
    if (document.getElementById('batch-collect')) {
      document.getElementById('batch-collect').addEventListener('click', function() {
        batchCollect();
      });
    }

    // 收益计算器
    if (document.getElementById('yield-calculator')) {
      document.getElementById('yield-calculator').addEventListener('click', function() {
        showYieldCalculator();
      });
    }

    // 导出数据
    if (document.getElementById('export-dividend')) {
      document.getElementById('export-dividend').addEventListener('click', function() {
        exportDividendData();
      });
    }

    // 原有的排序和搜索功能
    initOriginalFeatures();
  }

  // 初始化原有功能
  function initOriginalFeatures() {
    // 排序功能
    document.querySelectorAll('.sortable-header').forEach(header => {
      header.style.cursor = 'pointer';
      header.addEventListener('click', function() {
        const sortField = this.dataset.sort;
        const currentSort = new URLSearchParams(window.location.search).get('sort');
        let newSort = sortField;

        if (currentSort === sortField) {
          newSort = '-' + sortField;
        } else if (currentSort === '-' + sortField) {
          newSort = sortField;
        }

        const url = new URL(window.location);
        url.searchParams.set('sort', newSort);
        window.location.href = url.toString();
      });
    });

    // 搜索功能
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          document.getElementById('filter-form').submit();
        }
      });
    }

    // 年份和状态筛选
    if (document.getElementById('year-select')) {
      document.getElementById('year-select').addEventListener('change', function() {
        document.getElementById('filter-form').submit();
      });
    }

    if (document.getElementById('status-select')) {
      document.getElementById('status-select').addEventListener('change', function() {
        document.getElementById('filter-form').submit();
      });
    }
  }
</script>
{% endblock extra_js %}