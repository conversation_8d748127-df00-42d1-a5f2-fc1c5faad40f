from django.urls import path
from . import views
from .views.index_views import IndexView
from .views import market_index_views
from .views.fund_flow_views import (
    market_fund_flow,
    market_fund_flow_trend,
    api_market_fund_flow_trend,
    sector_fund_flow,
    sector_fund_flow_detail,
    api_sector_fund_flow_trend,
    stock_fund_flow,
    stock_fund_flow_detail,
    limit_list,
    StockLimitListView,
)


app_name = "market_data"

urlpatterns = [
    path("", IndexView.as_view(), name="index"),
    path("stocks/", views.stock_list, name="stock_list"),
    path("stocks/<str:code>/", views.stock_detail, name="stock_detail"),
    path("risk-warning/", views.risk_warning, name="risk_warning"),
    path("ipo-list/", views.ipo_list, name="ipo_list"),
    path("industry-board/", views.industry_board_list, name="industry_board_list"),
    path(
        "industry-board/<str:code>/",
        views.industry_board_detail,
        name="industry_board_detail",
    ),
    path("industry-chain/", views.industry_chain_list, name="industry_chain_list"),
    path("stock-history/<str:code>/", views.stock_history, name="stock_history"),
    path(
        "api/stock-chart/<str:code>/",
        views.api_stock_chart_data,
        name="api_stock_chart_data",
    ),
    path(
        "api/index-chart/<str:code>/",
        market_index_views.api_index_chart_data,
        name="api_index_chart_data",
    ),
    path("data-statistics/", views.data_statistics, name="data_statistics"),
    path("api/market-indices/", views.market_indices_api, name="market_indices_api"),

    path("board-concept/", views.board_concept, name="board_concept"),
    path(
        "board-concept/<str:board_code>/",
        views.board_concept_detail,
        name="board_concept_detail",
    ),
    path(
        "api/industry-board-chart/<str:code>/",
        views.api_industry_board_chart_data,
        name="api_industry_board_chart_data",
    ),
    path(
        "api/concept-board-chart/<str:code>/",
        views.api_concept_board_chart_data,
        name="api_concept_board_chart_data",
    ),
    path("market-index/", views.market_index_list, name="market_index_list"),
    path(
        "market-index/<str:code>/",
        views.market_index_detail,
        name="market_index_detail",
    ),
    path(
        "industry/<str:industry_name>/", views.industry_stocks, name="industry_stocks"
    ),
    path(
        "industry-stocks/<str:industry_name>/", views.api_industry_stocks, name="api_industry_stocks"
    ),
    path("top-list/", views.top_list, name="top_list"),
    path("top-list/<int:pk>/", views.top_list_detail, name="top_list_detail"),
    path("margin/", views.margin_list, name="margin_list"),
    path("margin/<int:pk>/", views.margin_detail, name="margin_detail"),
    path("comment/", views.comment_list, name="comment_list"),
    path("comment/<str:code>/", views.comment_detail, name="comment_detail"),
    path("active-broker/", views.active_broker_list, name="active_broker_list"),
    path(
        "active-broker/<str:broker_name>/",
        views.active_broker_detail,
        name="active_broker_detail",
    ),
    path("hk-stock-connect/", views.hk_stock_connect, name="hk_stock_connect"),
    # 资金流向相关URL
    path("market-fund-flow/", market_fund_flow, name="market_fund_flow"),
    path(
        "market-fund-flow-trend/", market_fund_flow_trend, name="market_fund_flow_trend"
    ),
    path(
        "api/market-fund-flow-trend/",
        api_market_fund_flow_trend,
        name="api_market_fund_flow_trend",
    ),
    path("sector-fund-flow/", sector_fund_flow, name="sector_fund_flow"),
    path(
        "sector-fund-flow/<str:sector_name>/",
        sector_fund_flow_detail,
        name="sector_fund_flow_detail",
    ),
    path(
        "api/sector-fund-flow-trend/<str:sector_name>/",
        api_sector_fund_flow_trend,
        name="api_sector_fund_flow_trend",
    ),
    path("stock-fund-flow/", stock_fund_flow, name="stock_fund_flow"),
    path(
        "stock-fund-flow/<str:code>/",
        stock_fund_flow_detail,
        name="stock_fund_flow_detail",
    ),
    # 涨跌停数据
    path("limit-list/", limit_list, name="limit_list"),
    path("limit-list-class/", StockLimitListView.as_view(), name="limit_list_class"),
]
