{% extends 'base.html' %}
{% load static %}

{% block title %}板块资金流向分析 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          <i class="ti ti-trending-up me-2"></i>
          板块资金流向分析
        </h2>
        <div class="text-muted mt-1">深度分析市场、板块的资金流向，发现投资机会</div>
      </div>
      <div class="col-auto">
        <div class="btn-list">
          <button id="compare-sectors" class="btn btn-primary">
            <i class="ti ti-chart-line"></i>
            板块对比
          </button>
          <button id="export-data" class="btn btn-outline-secondary">
            <i class="ti ti-download"></i>
            导出数据
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 市场资金流向趋势图 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-line me-2"></i>
              市场资金流向趋势
            </h3>
            <div class="card-actions">
              <select id="trend-period" class="form-select" style="width: auto;">
                <option value="7">近7天</option>
                <option value="15">近15天</option>
                <option value="30" selected>近30天</option>
                <option value="60">近60天</option>
              </select>
            </div>
          </div>
          <div class="card-body">
            <div id="market-fund-flow-chart" style="height: 350px;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 总净流入统计卡片 -->
    <div class="row mb-3">
      <div class="col-12">
        <div class="card total-stats-card">
          <div class="card-body">
            <div class="row">
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">总净流入</div>
                  <div class="h4 mb-0 number-display">
                    {% if total_net_inflow > 0 %}
                    <span class="text-up">+{{ total_net_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_net_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">超大单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_super_big_inflow > 0 %}
                    <span class="text-up">+{{ total_super_big_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_super_big_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">大单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_big_inflow > 0 %}
                    <span class="text-up">+{{ total_big_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_big_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">中单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_medium_inflow > 0 %}
                    <span class="text-up">+{{ total_medium_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_medium_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">小单净流入</div>
                  <div class="h5 mb-0 number-display">
                    {% if total_small_inflow > 0 %}
                    <span class="text-up">+{{ total_small_inflow }}亿</span>
                    {% else %}
                    <span class="text-down">{{ total_small_inflow }}亿</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="text-center">
                  <div class="text-muted small">板块统计</div>
                  <div class="h6 mb-0 number-display">
                    <span class="text-up">{{ inflow_sectors_count }}</span> /
                    <span class="text-down">{{ outflow_sectors_count }}</span>
                  </div>
                  <div class="text-muted small">流入/流出</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门板块和板块列表 -->
    <div class="row mb-4">
      <!-- 热门板块 -->
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-flame me-2"></i>
              热门板块 (近3天)
            </h3>
          </div>
          <div class="card-body">
            {% for sector in hot_sectors %}
            <div class="sector-item d-flex justify-content-between align-items-center py-2 border-bottom"
                 style="cursor: pointer;" onclick="goToSectorDetail('{{ sector.sector_name }}', '{{ sector.sector_type }}')">
              <div>
                <span class="fw-bold">{{ sector.sector_name }}</span>
                <span class="badge {% if sector.sector_type == '行业板块' %}bg-blue{% else %}bg-orange{% endif %} ms-2">
                  {{ sector.sector_type }}
                </span>
              </div>
              <div class="text-up fw-bold">
                +{{ sector.total_inflow }}亿
              </div>
            </div>
            {% empty %}
            <div class="text-muted text-center py-3">暂无热门板块数据</div>
            {% endfor %}
          </div>
        </div>
      </div>

      <!-- 板块资金流向表格 -->
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">{{ sector_type }}资金流向数据 ({{ selected_date|date:"Y-m-d" }})</h3>
            <div class="col-auto ms-auto d-print-none">
              <div class="d-flex">
                <div class="me-2">
                  <form method="get" class="d-flex">
                    <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
                    <select class="form-select ms-2" name="sector_type">
                      {% for type in sector_types %}
                      <option value="{{ type }}" {% if sector_type == type %}selected{% endif %}>{{ type }}</option>
                      {% endfor %}
                    </select>
                    <button type="submit" class="btn ms-2">查询</button>
                  </form>
                </div>
              </div>
            </div>
          </div>

      <!-- 筛选条件 -->
      <div class="card-body border-bottom">
        <form method="get" class="row g-3">
          <!-- 保持现有参数 -->
          <input type="hidden" name="date" value="{{ selected_date|date:'Y-m-d' }}">
          <input type="hidden" name="sector_type" value="{{ sector_type }}">

          <div class="col-md-2">
            <label class="form-label">搜索板块</label>
            <input type="text" class="form-control" name="q" value="{{ search_query }}" placeholder="板块名称">
          </div>

          <div class="col-md-2">
            <label class="form-label">净流入范围(亿)</label>
            <div class="input-group">
              <input type="number" class="form-control" name="min_inflow" value="{{ min_inflow }}" placeholder="最小" step="0.1">
              <span class="input-group-text">~</span>
              <input type="number" class="form-control" name="max_inflow" value="{{ max_inflow }}" placeholder="最大" step="0.1">
            </div>
          </div>

          <div class="col-md-2">
            <label class="form-label">涨跌幅范围(%)</label>
            <div class="input-group">
              <input type="number" class="form-control" name="min_rate" value="{{ min_rate }}" placeholder="最小" step="0.1">
              <span class="input-group-text">~</span>
              <input type="number" class="form-control" name="max_rate" value="{{ max_rate }}" placeholder="最大" step="0.1">
            </div>
          </div>

          <div class="col-md-2">
            <label class="form-label">排序方式</label>
            <select class="form-select" name="sort">
              <option value="-net_inflow_amount" {% if sort_by == '-net_inflow_amount' %}selected{% endif %}>主力净流入↓</option>
              <option value="net_inflow_amount" {% if sort_by == 'net_inflow_amount' %}selected{% endif %}>主力净流入↑</option>
              <option value="-net_inflow_rate" {% if sort_by == '-net_inflow_rate' %}selected{% endif %}>涨跌幅↓</option>
              <option value="net_inflow_rate" {% if sort_by == 'net_inflow_rate' %}selected{% endif %}>涨跌幅↑</option>
              <option value="-main_net_inflow_pct" {% if sort_by == '-main_net_inflow_pct' %}selected{% endif %}>净占比↓</option>
              <option value="main_net_inflow_pct" {% if sort_by == 'main_net_inflow_pct' %}selected{% endif %}>净占比↑</option>
            </select>
          </div>

          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">筛选</button>
              <a href="{% url 'market_data:sector_fund_flow' %}?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}" class="btn btn-outline-secondary">重置</a>
            </div>
          </div>
        </form>

        <!-- 快捷筛选按钮 -->
        <div class="mt-3">
          <div class="d-flex flex-wrap gap-2">
            <span class="text-muted me-2">快捷筛选：</span>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=1&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>1亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=5&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>5亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_inflow=10&sort=-net_inflow_amount" class="btn btn-sm btn-outline-success">净流入>10亿</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_rate=3&sort=-net_inflow_rate" class="btn btn-sm btn-outline-danger">涨幅>3%</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&min_rate=5&sort=-net_inflow_rate" class="btn btn-sm btn-outline-danger">涨幅>5%</a>
            <a href="?date={{ selected_date|date:'Y-m-d' }}&sector_type={{ sector_type }}&max_inflow=-1&sort=net_inflow_amount" class="btn btn-sm btn-outline-warning">净流出>1亿</a>
          </div>
        </div>
      </div>


      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>排名</th>
              <th>板块名称</th>
              <th>涨跌幅</th>
              <th>主力净流入</th>
              <th>主力净占比</th>
              <th>超大单净流入</th>
              <th>大单净流入</th>
              <th>中单净流入</th>
              <th>小单净流入</th>
              <th>领涨股</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for sector in sectors %}
            <tr>
              <td>{{ sector.rank }}</td>
              <td>
                <a href="{% url 'market_data:sector_fund_flow_detail' sector.sector_name %}?sector_type={{ sector_type }}" class="text-decoration-none">
                  {{ sector.sector_name }}
                </a>
              </td>
              <td>
                {% if sector.net_inflow_rate > 0 %}
                <span class="text-up">+{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% elif sector.net_inflow_rate < 0 %}
                <span class="text-down">{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% else %}
                <span class="text-muted">{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.net_inflow_amount > 0 %}
                <span class="text-up">+{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% elif sector.net_inflow_amount < 0 %}
                <span class="text-down">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.main_net_inflow_pct > 0 %}
                <span class="text-up">+{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% elif sector.main_net_inflow_pct < 0 %}
                <span class="text-down">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="text-muted">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.super_big_net_inflow > 0 %}
                <span class="text-up">+{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.super_big_net_inflow < 0 %}
                <span class="text-down">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.big_net_inflow > 0 %}
                <span class="text-up">+{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.big_net_inflow < 0 %}
                <span class="text-down">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.medium_net_inflow > 0 %}
                <span class="text-up">+{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.medium_net_inflow < 0 %}
                <span class="text-down">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.small_net_inflow > 0 %}
                <span class="text-up">+{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% elif sector.small_net_inflow < 0 %}
                <span class="text-down">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-muted">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>{{ sector.max_net_inflow_stock }}</td>
              <td>
                <button class="btn btn-sm btn-outline-primary compare-btn"
                        data-sector="{{ sector.sector_name }}" data-sector-type="{{ sector.sector_type }}">
                  <i class="ti ti-plus"></i>
                  对比
                </button>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="11" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有行业资金流向数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include "includes/pagination.html" with page_obj=sectors rows_per_page=rows_per_page show_flow_indicator=True %}
    </div>
  </div>

  <!-- 板块对比面板 -->
  <div id="comparison-panel" class="card mt-4" style="display: none;">
    <div class="card-header">
      <h3 class="card-title">
        <i class="ti ti-chart-line me-2"></i>
        板块趋势对比
      </h3>
      <div class="card-actions">
        <button id="clear-comparison" class="btn btn-sm btn-outline-secondary">
          清空对比
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="mb-3">
        <span class="text-muted">已选择板块：</span>
        <span id="selected-sectors"></span>
      </div>
      <div id="comparison-chart" style="height: 400px;"></div>
    </div>
  </div>

  <!-- 投资机会发现面板 -->
  <div class="card mt-4">
    <div class="card-header">
      <h3 class="card-title">
        <i class="ti ti-bulb me-2"></i>
        投资机会发现
      </h3>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <div class="alert alert-info">
            <h5>连续流入板块</h5>
            <p class="mb-0">寻找连续多日资金净流入的板块</p>
            <button class="btn btn-sm btn-info mt-2" onclick="findContinuousInflow()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-success">
            <h5>放量上涨板块</h5>
            <p class="mb-0">资金流入且涨幅较大的板块</p>
            <button class="btn btn-sm btn-success mt-2" onclick="findVolumeRise()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-warning">
            <h5>资金分歧板块</h5>
            <p class="mb-0">大单流入但小单流出的板块</p>
            <button class="btn btn-sm btn-warning mt-2" onclick="findFundDivergence()">
              查找
            </button>
          </div>
        </div>
        <div class="col-md-3">
          <div class="alert alert-primary">
            <h5>低估值机会</h5>
            <p class="mb-0">资金流入但估值相对较低</p>
            <button class="btn btn-sm btn-primary mt-2" onclick="findValueOpportunity()">
              查找
            </button>
          </div>
        </div>
      </div>

      <div id="opportunity-results" class="mt-3" style="display: none;">
        <h5>分析结果</h5>
        <div id="opportunity-content"></div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  /* 总净流入统计卡片特殊美化 */
  .total-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
    border: none;
    margin-bottom: 30px;
  }

  .total-stats-card .card-body {
    padding: 30px;
  }

  .total-stats-card .h4, .total-stats-card .h5, .total-stats-card .h6 {
    color: white !important;
  }

  /* 优化数值显示 */
  .number-display {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  /* 表格行悬停效果 */
  .table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }

  /* 优化表格数值列 */
  .table td:nth-child(n+3) {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
  }

  /* 热门板块样式 */
  .sector-item {
    transition: all 0.2s ease;
  }

  .sector-item:hover {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
  }

  /* 按钮美化 */
  .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  /* 快捷筛选按钮美化 */
  .btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 16px;
  }

  /* 对比按钮样式 */
  .compare-btn {
    transition: all 0.2s ease;
  }

  .compare-btn:hover {
    transform: scale(1.05);
  }

  /* 投资机会发现面板样式 */
  .alert {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .alert h5 {
    font-weight: 600;
    margin-bottom: 8px;
  }

  /* 动画效果 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .card {
    animation: fadeInUp 0.6s ease-out;
  }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
<script>
  // 板块对比功能
  let selectedSectors = [];
  let comparisonChart = null;
  let marketChart = null;

  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // 添加数值格式化动画
    const numberElements = document.querySelectorAll('.number-display');
    numberElements.forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(10px)';
      setTimeout(() => {
        el.style.transition = 'all 0.5s ease';
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      }, 100);
    });

    // 初始化市场资金流向图表
    loadMarketFundFlowChart();

    // 对比按钮点击事件
    document.querySelectorAll('.compare-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const sectorName = this.dataset.sector;
        const sectorType = this.dataset.sectorType;
        addToComparison(sectorName, sectorType);
      });
    });

    // 清空对比
    document.getElementById('clear-comparison').addEventListener('click', function() {
      selectedSectors = [];
      updateComparisonDisplay();
      document.getElementById('comparison-panel').style.display = 'none';
    });

    // 导出数据
    document.getElementById('export-data').addEventListener('click', function() {
      exportTableData();
    });

    // 趋势图时间周期切换
    document.getElementById('trend-period').addEventListener('change', function() {
      const period = this.value;
      loadMarketFundFlowChart(period);
    });
  });

  // 加载市场资金流向图表
  function loadMarketFundFlowChart(days = 30) {
    const chartDom = document.getElementById('market-fund-flow-chart');
    if (!chartDom) return;

    if (!marketChart) {
      marketChart = echarts.init(chartDom);
    }

    // 模拟数据 - 实际应该从API获取
    const dates = [];
    const mainInflow = [];
    const superBigInflow = [];
    const bigInflow = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));

      // 模拟数据
      mainInflow.push((Math.random() - 0.5) * 200);
      superBigInflow.push((Math.random() - 0.5) * 120);
      bigInflow.push((Math.random() - 0.5) * 80);
    }

    const option = {
      title: {
        text: `市场资金流向趋势 (近${days}天)`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `<div style="font-weight:bold;">${params[0].name}</div>`;
          params.forEach(param => {
            const color = param.color;
            const value = param.value >= 0 ? `+${param.value.toFixed(1)}` : param.value.toFixed(1);
            result += `<div style="margin:2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>
              ${param.seriesName}: <span style="font-weight:bold;color:${param.value >= 0 ? '#dc2626' : '#16a34a'}">${value}亿</span>
            </div>`;
          });
          return result;
        }
      },
      legend: {
        data: ['主力净流入', '超大单净流入', '大单净流入'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          fontSize: 11
        }
      },
      yAxis: {
        type: 'value',
        name: '净流入(亿元)',
        axisLabel: {
          formatter: '{value}亿'
        }
      },
      series: [
        {
          name: '主力净流入',
          type: 'line',
          data: mainInflow,
          itemStyle: { color: '#ef4444' },
          smooth: true,
          symbol: 'circle',
          symbolSize: 4
        },
        {
          name: '超大单净流入',
          type: 'line',
          data: superBigInflow,
          itemStyle: { color: '#f97316' },
          smooth: true,
          symbol: 'circle',
          symbolSize: 4
        },
        {
          name: '大单净流入',
          type: 'line',
          data: bigInflow,
          itemStyle: { color: '#3b82f6' },
          smooth: true,
          symbol: 'circle',
          symbolSize: 4
        }
      ]
    };

    marketChart.setOption(option);
  }

  // 板块对比功能
  function addToComparison(sectorName, sectorType) {
    if (selectedSectors.find(s => s.name === sectorName)) {
      return;
    }

    if (selectedSectors.length >= 5) {
      alert('最多只能对比5个板块');
      return;
    }

    selectedSectors.push({ name: sectorName, type: sectorType });
    updateComparisonDisplay();

    if (selectedSectors.length >= 2) {
      loadComparisonData();
    }
  }

  function updateComparisonDisplay() {
    const container = document.getElementById('selected-sectors');
    container.innerHTML = selectedSectors.map(sector =>
      `<span class="badge bg-primary me-1">${sector.name}</span>`
    ).join('');

    if (selectedSectors.length > 0) {
      document.getElementById('comparison-panel').style.display = 'block';
    }
  }

  function loadComparisonData() {
    // 模拟对比数据加载
    setTimeout(() => {
      renderComparisonChart();
    }, 500);
  }

  function renderComparisonChart() {
    if (!comparisonChart) {
      comparisonChart = echarts.init(document.getElementById('comparison-chart'));
    }

    // 模拟对比数据
    const dates = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
    }

    const series = selectedSectors.map((sector, index) => {
      const data = dates.map(() => (Math.random() - 0.5) * 50);
      const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6'];

      return {
        name: sector.name,
        type: 'line',
        data: data,
        smooth: true,
        itemStyle: { color: colors[index] },
        symbol: 'circle',
        symbolSize: 4
      };
    });

    const option = {
      title: {
        text: '板块资金流向对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          let result = `<div style="font-weight:bold;">${params[0].name}</div>`;
          params.forEach(param => {
            const color = param.color;
            const value = param.value >= 0 ? `+${param.value.toFixed(1)}` : param.value.toFixed(1);
            result += `<div style="margin:2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>
              ${param.seriesName}: <span style="font-weight:bold;">${value}亿</span>
            </div>`;
          });
          return result;
        }
      },
      legend: {
        data: selectedSectors.map(s => s.name),
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value',
        name: '资金流入(亿元)',
        axisLabel: {
          formatter: '{value}亿'
        }
      },
      series: series
    };

    comparisonChart.setOption(option);
  }

  // 导出数据功能
  function exportTableData() {
    const table = document.querySelector('.table');
    const rows = Array.from(table.querySelectorAll('tr'));

    const csvContent = rows.map(row => {
      const cells = Array.from(row.querySelectorAll('th, td'));
      return cells.slice(0, -1).map(cell => cell.textContent.trim()).join(','); // 排除操作列
    }).join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '板块资金流向分析.csv';
    link.click();
  }

  // 投资机会发现功能
  function findContinuousInflow() {
    showOpportunityResults('连续流入板块', [
      { name: '新能源汽车', type: '概念板块', days: 5, amount: 45.6, reason: '连续5日净流入，政策利好' },
      { name: '半导体', type: '行业板块', days: 4, amount: 32.1, reason: '连续4日净流入，技术突破' },
      { name: '医疗器械', type: '行业板块', days: 3, amount: 28.9, reason: '连续3日净流入，需求增长' }
    ]);
  }

  function findVolumeRise() {
    showOpportunityResults('放量上涨板块', [
      { name: '人工智能', type: '概念板块', rate: 8.5, amount: 52.3, reason: '涨幅8.5%，资金大幅流入' },
      { name: '5G通信', type: '概念板块', rate: 6.2, amount: 38.7, reason: '涨幅6.2%，成交活跃' },
      { name: '新材料', type: '行业板块', rate: 5.8, amount: 29.4, reason: '涨幅5.8%，资金追捧' }
    ]);
  }

  function findFundDivergence() {
    showOpportunityResults('资金分歧板块', [
      { name: '房地产', type: '行业板块', big: 15.6, small: -12.3, reason: '大资金看好，散户观望' },
      { name: '银行', type: '行业板块', big: 22.1, small: -8.9, reason: '机构增持，个人减持' },
      { name: '保险', type: '行业板块', big: 18.7, small: -15.2, reason: '价值投资，短期分歧' }
    ]);
  }

  function findValueOpportunity() {
    showOpportunityResults('低估值机会', [
      { name: '钢铁', type: '行业板块', pe: 8.5, amount: 12.6, reason: 'PE仅8.5倍，资金开始关注' },
      { name: '煤炭', type: '行业板块', pe: 6.8, amount: 18.9, reason: 'PE历史低位，价值凸显' },
      { name: '化工', type: '行业板块', pe: 12.3, amount: 15.4, reason: '估值合理，基本面改善' }
    ]);
  }

  function showOpportunityResults(title, data) {
    const resultsDiv = document.getElementById('opportunity-results');
    const contentDiv = document.getElementById('opportunity-content');

    let html = `<h6>${title}</h6><div class="table-responsive">
      <table class="table table-sm">
        <thead>
          <tr>
            <th>板块名称</th>
            <th>类型</th>`;

    if (title.includes('连续流入')) {
      html += '<th>连续天数</th><th>累计流入(亿)</th>';
    } else if (title.includes('放量上涨')) {
      html += '<th>涨跌幅</th><th>资金流入(亿)</th>';
    } else if (title.includes('资金分歧')) {
      html += '<th>大单流入(亿)</th><th>小单流入(亿)</th>';
    } else if (title.includes('低估值')) {
      html += '<th>PE倍数</th><th>资金流入(亿)</th>';
    }

    html += '<th>分析原因</th></tr></thead><tbody>';

    data.forEach(item => {
      html += `<tr>
        <td><span class="fw-bold">${item.name}</span></td>
        <td><span class="badge ${item.type === '行业板块' ? 'bg-blue' : 'bg-orange'}">${item.type}</span></td>`;

      if (item.days !== undefined) {
        html += `<td class="text-success">${item.days}天</td><td class="text-success">+${item.amount}</td>`;
      } else if (item.rate !== undefined) {
        html += `<td class="text-danger">+${item.rate}%</td><td class="text-success">+${item.amount}</td>`;
      } else if (item.big !== undefined) {
        html += `<td class="text-success">+${item.big}</td><td class="text-danger">${item.small}</td>`;
      } else if (item.pe !== undefined) {
        html += `<td>${item.pe}</td><td class="text-success">+${item.amount}</td>`;
      }

      html += `<td class="text-muted">${item.reason}</td></tr>`;
    });

    html += '</tbody></table></div>';

    contentDiv.innerHTML = html;
    resultsDiv.style.display = 'block';

    // 滚动到结果区域
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
  }

  // 跳转到板块详情页
  function goToSectorDetail(sectorName, sectorType) {
    const encodedName = encodeURIComponent(sectorName);
    const url = `/market_data/sector-fund-flow/${encodedName}/?sector_type=${encodeURIComponent(sectorType)}`;
    window.location.href = url;
  }

  // 响应式处理
  window.addEventListener('resize', function() {
    if (comparisonChart) {
      comparisonChart.resize();
    }
    if (marketChart) {
      marketChart.resize();
    }
  });
</script>
{% endblock %}
